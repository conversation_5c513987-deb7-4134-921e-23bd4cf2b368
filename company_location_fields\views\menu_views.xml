<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Menu for Company Location Configuration -->
        <menuitem id="menu_company_location_config"
                  name="Company Location"
                  parent="base.menu_administration"
                  sequence="50"
                  groups="base.group_system"/>

        <!-- Company Countries Menu -->
        <menuitem id="menu_company_country"
                  name="Countries"
                  parent="menu_company_location_config"
                  action="action_company_country"
                  sequence="10"
                  groups="base.group_system"/>

        <!-- General Managements Menu -->
        <menuitem id="menu_general_management"
                  name="General Managements"
                  parent="menu_company_location_config"
                  action="action_general_management"
                  sequence="20"
                  groups="base.group_system"/>

        <!-- Company Branches Menu -->
        <menuitem id="menu_company_branch"
                  name="Branches"
                  parent="menu_company_location_config"
                  action="action_company_branch"
                  sequence="30"
                  groups="base.group_system"/>

        <!-- Separator -->
        <menuitem id="menu_company_location_separator"
                  name="Company Location Reports"
                  parent="menu_company_location_config"
                  sequence="100"
                  groups="base.group_system"/>

        <!-- Company Location Report Action -->
        <record id="action_company_location_report" model="ir.actions.act_window">
            <field name="name">Company Location Report</field>
            <field name="res_model">res.company</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="domain">[]</field>
            <field name="context">{
                'search_default_group_by_country': 1,
                'search_default_active': 1
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    View companies organized by location!
                </p>
                <p>
                    This report shows all companies grouped by their location information
                    including country, general management, and branch.
                </p>
            </field>
        </record>

        <!-- Company Location Report Menu -->
        <menuitem id="menu_company_location_report"
                  name="Companies by Location"
                  parent="menu_company_location_config"
                  action="action_company_location_report"
                  sequence="110"
                  groups="base.group_system"/>
    </data>
</odoo>
