<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Company Countries -->
        <record id="country_saudi_arabia" model="company.country">
            <field name="name">Saudi Arabia</field>
            <field name="code">SA</field>
            <field name="sequence">10</field>
            <field name="description">Kingdom of Saudi Arabia</field>
        </record>

        <record id="country_uae" model="company.country">
            <field name="name">United Arab Emirates</field>
            <field name="code">AE</field>
            <field name="sequence">20</field>
            <field name="description">United Arab Emirates</field>
        </record>

        <record id="country_egypt" model="company.country">
            <field name="name">Egypt</field>
            <field name="code">EG</field>
            <field name="sequence">30</field>
            <field name="description">Arab Republic of Egypt</field>
        </record>

        <record id="country_jordan" model="company.country">
            <field name="name">Jordan</field>
            <field name="code">JO</field>
            <field name="sequence">40</field>
            <field name="description">Hashemite Kingdom of Jordan</field>
        </record>

        <!-- Demo General Managements for Saudi Arabia -->
        <record id="gm_riyadh" model="general.management">
            <field name="name">Riyadh General Management</field>
            <field name="code">RYD-GM</field>
            <field name="country_id" ref="country_saudi_arabia"/>
            <field name="sequence">10</field>
            <field name="manager_name">Ahmed Al-Rashid</field>
            <field name="phone">+966-11-1234567</field>
            <field name="email"><EMAIL></field>
            <field name="address">King Fahd Road, Riyadh, Saudi Arabia</field>
        </record>

        <record id="gm_jeddah" model="general.management">
            <field name="name">Jeddah General Management</field>
            <field name="code">JED-GM</field>
            <field name="country_id" ref="country_saudi_arabia"/>
            <field name="sequence">20</field>
            <field name="manager_name">Mohammed Al-Harbi</field>
            <field name="phone">+966-12-1234567</field>
            <field name="email"><EMAIL></field>
            <field name="address">Corniche Road, Jeddah, Saudi Arabia</field>
        </record>

        <record id="gm_dammam" model="general.management">
            <field name="name">Eastern Province General Management</field>
            <field name="code">EP-GM</field>
            <field name="country_id" ref="country_saudi_arabia"/>
            <field name="sequence">30</field>
            <field name="manager_name">Khalid Al-Otaibi</field>
            <field name="phone">+966-13-1234567</field>
            <field name="email"><EMAIL></field>
            <field name="address">King Abdulaziz Road, Dammam, Saudi Arabia</field>
        </record>

        <!-- Demo General Managements for UAE -->
        <record id="gm_dubai" model="general.management">
            <field name="name">Dubai General Management</field>
            <field name="code">DXB-GM</field>
            <field name="country_id" ref="country_uae"/>
            <field name="sequence">10</field>
            <field name="manager_name">Omar Al-Mansouri</field>
            <field name="phone">+971-4-1234567</field>
            <field name="email"><EMAIL></field>
            <field name="address">Sheikh Zayed Road, Dubai, UAE</field>
        </record>

        <record id="gm_abudhabi" model="general.management">
            <field name="name">Abu Dhabi General Management</field>
            <field name="code">AUH-GM</field>
            <field name="country_id" ref="country_uae"/>
            <field name="sequence">20</field>
            <field name="manager_name">Saeed Al-Nahyan</field>
            <field name="phone">+971-2-1234567</field>
            <field name="email"><EMAIL></field>
            <field name="address">Corniche Road, Abu Dhabi, UAE</field>
        </record>

        <!-- Demo General Managements for Egypt -->
        <record id="gm_cairo" model="general.management">
            <field name="name">Cairo General Management</field>
            <field name="code">CAI-GM</field>
            <field name="country_id" ref="country_egypt"/>
            <field name="sequence">10</field>
            <field name="manager_name">Hassan Mohamed</field>
            <field name="phone">+20-2-12345678</field>
            <field name="email"><EMAIL></field>
            <field name="address">Tahrir Square, Cairo, Egypt</field>
        </record>

        <record id="gm_alexandria" model="general.management">
            <field name="name">Alexandria General Management</field>
            <field name="code">ALX-GM</field>
            <field name="country_id" ref="country_egypt"/>
            <field name="sequence">20</field>
            <field name="manager_name">Mahmoud Ali</field>
            <field name="phone">+20-3-12345678</field>
            <field name="email"><EMAIL></field>
            <field name="address">Corniche Road, Alexandria, Egypt</field>
        </record>

        <!-- Demo Company Branches -->
        <record id="branch_main_office" model="company.branch">
            <field name="name">Main Office</field>
            <field name="code">MAIN</field>
            <field name="branch_type">main</field>
            <field name="sequence">10</field>
            <field name="manager_name">General Manager</field>
            <field name="description">Main headquarters office</field>
        </record>

        <record id="branch_north" model="company.branch">
            <field name="name">North Branch</field>
            <field name="code">NORTH</field>
            <field name="branch_type">sub</field>
            <field name="sequence">20</field>
            <field name="manager_name">North Branch Manager</field>
            <field name="description">Northern region branch</field>
        </record>

        <record id="branch_south" model="company.branch">
            <field name="name">South Branch</field>
            <field name="code">SOUTH</field>
            <field name="branch_type">sub</field>
            <field name="sequence">30</field>
            <field name="manager_name">South Branch Manager</field>
            <field name="description">Southern region branch</field>
        </record>

        <record id="branch_warehouse" model="company.branch">
            <field name="name">Central Warehouse</field>
            <field name="code">WAREHOUSE</field>
            <field name="branch_type">warehouse</field>
            <field name="sequence">40</field>
            <field name="manager_name">Warehouse Manager</field>
            <field name="description">Main storage and distribution center</field>
        </record>

        <record id="branch_sales_office" model="company.branch">
            <field name="name">Sales Office</field>
            <field name="code">SALES</field>
            <field name="branch_type">office</field>
            <field name="sequence">50</field>
            <field name="manager_name">Sales Manager</field>
            <field name="description">Dedicated sales and customer service office</field>
        </record>
    </data>
</odoo>
