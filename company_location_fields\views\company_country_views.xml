<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Company Country Tree View -->
        <record id="view_company_country_tree" model="ir.ui.view">
            <field name="name">company.country.tree</field>
            <field name="model">company.country</field>
            <field name="arch" type="xml">
                <tree string="Company Countries" default_order="sequence, name">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="general_managements_count" string="General Managements"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Company Country Form View -->
        <record id="view_company_country_form" model="ir.ui.view">
            <field name="name">company.country.form</field>
            <field name="model">company.country</field>
            <field name="arch" type="xml">
                <form string="Company Country">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                                <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name" placeholder="Country Name..."/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="code" placeholder="e.g., SA, AE, EG"/>
                                <field name="sequence"/>
                            </group>
                            <group>
                                <field name="general_managements_count" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Description" name="description">
                                <field name="description" placeholder="Additional information about the country..."/>
                            </page>
                            
                            <page string="General Managements" name="general_managements">
                                <field name="general_management_ids" context="{'default_country_id': active_id}">
                                    <tree editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="code"/>
                                        <field name="manager_name"/>
                                        <field name="phone"/>
                                        <field name="active"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Companies" name="companies">
                                <field name="company_ids" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="general_management_id"/>
                                        <field name="company_branch_id"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Company Country Search View -->
        <record id="view_company_country_search" model="ir.ui.view">
            <field name="name">company.country.search</field>
            <field name="model">company.country</field>
            <field name="arch" type="xml">
                <search string="Search Company Countries">
                    <field name="name" string="Country"/>
                    <field name="code" string="Code"/>
                    <field name="description"/>
                    
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>

                    <separator/>
                    <filter string="Has General Managements" name="has_managements"
                            domain="[('general_management_ids', '!=', False)]"/>
                    <filter string="No General Managements" name="no_managements"
                            domain="[('general_management_ids', '=', False)]"/>
                </search>
            </field>
        </record>

        <!-- Company Country Action -->
        <record id="action_company_country" model="ir.actions.act_window">
            <field name="name">Company Countries</field>
            <field name="res_model">company.country</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_company_country_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first company country!
                </p>
                <p>
                    Company countries are used to organize your companies by geographical location.
                    Each country can have multiple general managements.
                </p>
            </field>
        </record>
    </data>
</odoo>
