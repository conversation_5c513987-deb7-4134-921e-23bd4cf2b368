# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* company_location_fields
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: 2024-01-01 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__active
#: model:ir.model.fields,field_description:company_location_fields.field_company_country__active
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__active
msgid "Active"
msgstr "نشط"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__address
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__address
msgid "Address"
msgstr "العنوان"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__manager_name
msgid "Branch Manager"
msgstr "مدير الفرع"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__name
msgid "Branch Name"
msgstr "اسم الفرع"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__branch_type
msgid "Branch Type"
msgstr "نوع الفرع"

#. module: company_location_fields
#: model:ir.actions.act_window,name:company_location_fields.action_company_branch
#: model:ir.ui.menu,name:company_location_fields.menu_company_branch
msgid "Branches"
msgstr "الفروع"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__code
msgid "Branch Code"
msgstr "رمز الفرع"

#. module: company_location_fields
#: model:ir.actions.act_window,name:company_location_fields.action_company_location_report
#: model:ir.ui.menu,name:company_location_fields.menu_company_location_report
msgid "Companies by Location"
msgstr "الشركات حسب الموقع"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_res_company__company_branch_id
msgid "Company Branch"
msgstr "فرع الشركة"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_res_company__company_country_id
msgid "Company Country"
msgstr "بلد الشركة"

#. module: company_location_fields
#: model:ir.actions.act_window,name:company_location_fields.action_company_country
#: model:ir.ui.menu,name:company_location_fields.menu_company_country
msgid "Countries"
msgstr "البلدان"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_country__code
msgid "Country Code"
msgstr "رمز البلد"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_country__name
msgid "Country Name"
msgstr "اسم البلد"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__code
msgid "Code"
msgstr "الرمز"

#. module: company_location_fields
#: model:ir.ui.menu,name:company_location_fields.menu_company_location_config
msgid "Company Location"
msgstr "موقع الشركة"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__description
#: model:ir.model.fields,field_description:company_location_fields.field_company_country__description
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__description
msgid "Description"
msgstr "الوصف"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__email
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__email
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_res_company__general_management_id
msgid "General Management"
msgstr "الإدارة العامة"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__name
msgid "General Management Name"
msgstr "اسم الإدارة العامة"

#. module: company_location_fields
#: model:ir.actions.act_window,name:company_location_fields.action_general_management
#: model:ir.ui.menu,name:company_location_fields.menu_general_management
msgid "General Managements"
msgstr "الإدارات العامة"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__manager_name
msgid "Manager Name"
msgstr "اسم المدير"

#. module: company_location_fields
#: model:ir.model.fields,selection:company_location_fields.field_company_branch__branch_type
msgid "Main Branch"
msgstr "الفرع الرئيسي"

#. module: company_location_fields
#: model:ir.model.fields,selection:company_location_fields.field_company_branch__branch_type
msgid "Office"
msgstr "مكتب"

#. module: company_location_fields
#: model:ir.model.fields,selection:company_location_fields.field_company_branch__branch_type
msgid "Other"
msgstr "أخرى"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__phone
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__phone
msgid "Phone"
msgstr "الهاتف"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_company_branch__sequence
#: model:ir.model.fields,field_description:company_location_fields.field_company_country__sequence
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__sequence
msgid "Sequence"
msgstr "التسلسل"

#. module: company_location_fields
#: model:ir.model.fields,selection:company_location_fields.field_company_branch__branch_type
msgid "Sub Branch"
msgstr "فرع فرعي"

#. module: company_location_fields
#: model:ir.model.fields,selection:company_location_fields.field_company_branch__branch_type
msgid "Warehouse"
msgstr "مستودع"

#. module: company_location_fields
#: model:ir.model.fields,field_description:company_location_fields.field_general_management__country_id
msgid "Country"
msgstr "البلد"

#. module: company_location_fields
#: model:company.country,name:company_location_fields.country_saudi_arabia
msgid "Saudi Arabia"
msgstr "المملكة العربية السعودية"

#. module: company_location_fields
#: model:company.country,name:company_location_fields.country_uae
msgid "United Arab Emirates"
msgstr "دولة الإمارات العربية المتحدة"

#. module: company_location_fields
#: model:company.country,name:company_location_fields.country_egypt
msgid "Egypt"
msgstr "جمهورية مصر العربية"

#. module: company_location_fields
#: model:company.country,name:company_location_fields.country_jordan
msgid "Jordan"
msgstr "المملكة الأردنية الهاشمية"

#. module: company_location_fields
#: model:general.management,name:company_location_fields.gm_riyadh
msgid "Riyadh General Management"
msgstr "الإدارة العامة للرياض"

#. module: company_location_fields
#: model:general.management,name:company_location_fields.gm_jeddah
msgid "Jeddah General Management"
msgstr "الإدارة العامة لجدة"

#. module: company_location_fields
#: model:general.management,name:company_location_fields.gm_dammam
msgid "Eastern Province General Management"
msgstr "الإدارة العامة للمنطقة الشرقية"

#. module: company_location_fields
#: model:general.management,name:company_location_fields.gm_dubai
msgid "Dubai General Management"
msgstr "الإدارة العامة لدبي"

#. module: company_location_fields
#: model:general.management,name:company_location_fields.gm_abudhabi
msgid "Abu Dhabi General Management"
msgstr "الإدارة العامة لأبوظبي"

#. module: company_location_fields
#: model:general.management,name:company_location_fields.gm_cairo
msgid "Cairo General Management"
msgstr "الإدارة العامة للقاهرة"

#. module: company_location_fields
#: model:general.management,name:company_location_fields.gm_alexandria
msgid "Alexandria General Management"
msgstr "الإدارة العامة للإسكندرية"

#. module: company_location_fields
#: model:company.branch,name:company_location_fields.branch_main_office
msgid "Main Office"
msgstr "المكتب الرئيسي"

#. module: company_location_fields
#: model:company.branch,name:company_location_fields.branch_north
msgid "North Branch"
msgstr "الفرع الشمالي"

#. module: company_location_fields
#: model:company.branch,name:company_location_fields.branch_south
msgid "South Branch"
msgstr "الفرع الجنوبي"

#. module: company_location_fields
#: model:company.branch,name:company_location_fields.branch_warehouse
msgid "Central Warehouse"
msgstr "المستودع المركزي"

#. module: company_location_fields
#: model:company.branch,name:company_location_fields.branch_sales_office
msgid "Sales Office"
msgstr "مكتب المبيعات"
