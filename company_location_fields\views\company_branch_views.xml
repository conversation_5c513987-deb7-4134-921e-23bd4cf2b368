<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Company Branch Tree View -->
        <record id="view_company_branch_tree" model="ir.ui.view">
            <field name="name">company.branch.tree</field>
            <field name="model">company.branch</field>
            <field name="arch" type="xml">
                <tree string="Company Branches" default_order="sequence, name">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="branch_type"/>
                    <field name="manager_name"/>
                    <field name="phone"/>
                    <field name="companies_count" string="Companies"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Company Branch Form View -->
        <record id="view_company_branch_form" model="ir.ui.view">
            <field name="name">company.branch.form</field>
            <field name="model">company.branch</field>
            <field name="arch" type="xml">
                <form string="Company Branch">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                                <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name" placeholder="Branch Name..."/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="code" placeholder="e.g., BR001"/>
                                <field name="branch_type"/>
                                <field name="sequence"/>
                            </group>
                            <group>
                                <field name="manager_name" placeholder="Branch Manager Name"/>
                                <field name="phone" widget="phone"/>
                                <field name="email" widget="email"/>
                                <field name="companies_count" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Address &amp; Contact" name="contact">
                                <group>
                                    <field name="address" placeholder="Full address of the branch..."/>
                                </group>
                            </page>
                            
                            <page string="Description" name="description">
                                <field name="description" placeholder="Additional information about the branch..."/>
                            </page>
                            
                            <page string="Companies" name="companies">
                                <field name="company_ids" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="company_country_id"/>
                                        <field name="general_management_id"/>
                                        <field name="phone"/>
                                        <field name="email"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Company Branch Search View -->
        <record id="view_company_branch_search" model="ir.ui.view">
            <field name="name">company.branch.search</field>
            <field name="model">company.branch</field>
            <field name="arch" type="xml">
                <search string="Search Company Branches">
                    <field name="name" string="Branch"/>
                    <field name="code" string="Code"/>
                    <field name="branch_type" string="Type"/>
                    <field name="manager_name" string="Manager"/>
                    <field name="phone"/>
                    <field name="email"/>
                    
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    
                    <separator/>
                    <filter string="Main Branches" name="main_branches" 
                            domain="[('branch_type', '=', 'main')]"/>
                    <filter string="Sub Branches" name="sub_branches" 
                            domain="[('branch_type', '=', 'sub')]"/>
                    <filter string="Offices" name="offices" 
                            domain="[('branch_type', '=', 'office')]"/>
                    <filter string="Warehouses" name="warehouses" 
                            domain="[('branch_type', '=', 'warehouse')]"/>
                    
                    <separator/>
                    <filter string="Has Companies" name="has_companies"
                            domain="[('company_ids', '!=', False)]"/>
                    <filter string="No Companies" name="no_companies"
                            domain="[('company_ids', '=', False)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Branch Type" name="group_by_type" 
                                context="{'group_by': 'branch_type'}"/>
                        <filter string="Manager" name="group_by_manager" 
                                context="{'group_by': 'manager_name'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Company Branch Action -->
        <record id="action_company_branch" model="ir.actions.act_window">
            <field name="name">Company Branches</field>
            <field name="res_model">company.branch</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_company_branch_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first company branch!
                </p>
                <p>
                    Company branches represent physical locations or organizational units.
                    They can be main branches, sub branches, offices, warehouses, or other types.
                </p>
            </field>
        </record>
    </data>
</odoo>
