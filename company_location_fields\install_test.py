#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick installation test script for Company Location Fields module
"""

import sys
import os

def test_module_structure():
    """Test if all required files exist"""
    required_files = [
        '__manifest__.py',
        '__init__.py',
        'models/__init__.py',
        'models/company_country.py',
        'models/general_management.py',
        'models/company_branch.py',
        'models/res_company.py',
        'views/company_country_views.xml',
        'views/general_management_views.xml',
        'views/company_branch_views.xml',
        'views/res_company_views.xml',
        'views/menu_views.xml',
        'security/ir.model.access.csv',
        'data/demo_data.xml',
        'i18n/ar.po',
        'README.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All required files exist!")
        return True

def test_manifest():
    """Test manifest file"""
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check for required keys
        required_keys = ['name', 'version', 'depends', 'data', 'installable']
        for key in required_keys:
            if f"'{key}'" not in content and f'"{key}"' not in content:
                print(f"❌ Missing required key in manifest: {key}")
                return False
        
        print("✅ Manifest file is valid!")
        return True
    except Exception as e:
        print(f"❌ Error reading manifest: {e}")
        return False

def test_xml_syntax():
    """Basic XML syntax test"""
    import xml.etree.ElementTree as ET
    
    xml_files = [
        'views/company_country_views.xml',
        'views/general_management_views.xml',
        'views/company_branch_views.xml',
        'views/res_company_views.xml',
        'views/menu_views.xml',
        'data/demo_data.xml'
    ]
    
    for xml_file in xml_files:
        try:
            ET.parse(xml_file)
            print(f"✅ {xml_file} - XML syntax OK")
        except ET.ParseError as e:
            print(f"❌ {xml_file} - XML syntax error: {e}")
            return False
        except Exception as e:
            print(f"❌ {xml_file} - Error: {e}")
            return False
    
    return True

def test_python_syntax():
    """Basic Python syntax test"""
    python_files = [
        '__init__.py',
        'models/__init__.py',
        'models/company_country.py',
        'models/general_management.py',
        'models/company_branch.py',
        'models/res_company.py'
    ]
    
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Basic syntax check
            compile(content, py_file, 'exec')
            print(f"✅ {py_file} - Python syntax OK")
        except SyntaxError as e:
            print(f"❌ {py_file} - Syntax error: {e}")
            return False
        except Exception as e:
            print(f"❌ {py_file} - Error: {e}")
            return False
    
    return True

def main():
    """Main test function"""
    print("🔍 Testing Company Location Fields Module...")
    print("=" * 50)
    
    # Change to module directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    tests = [
        ("Module Structure", test_module_structure),
        ("Manifest File", test_manifest),
        ("XML Syntax", test_xml_syntax),
        ("Python Syntax", test_python_syntax)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Module is ready for installation.")
        print("\n📝 Next steps:")
        print("1. Copy the module to your Odoo addons directory")
        print("2. Restart Odoo server")
        print("3. Update Apps List")
        print("4. Install 'Company Location Fields' module")
    else:
        print("❌ Some tests failed. Please fix the issues before installation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
