# -*- coding: utf-8 -*-
{
    'name': 'Company Location Fields',
    'version': '********.0',
    'category': 'Base',
    'summary': 'Add Country, General Management and Branch fields to Company',
    'description': """
Company Location Fields
=======================

This module adds three new fields to the Company model:

1. **Country**: Configurable list of countries managed from Technical Settings
2. **General Management**: Linked to the selected country
3. **Branch**: Configurable list of branches

Features:
---------
* Hierarchical relationship: Country -> General Management -> Branch
* Bilingual support (Arabic/English)
* Technical Settings configuration
* Professional UI integration

Installation:
-------------
1. Install the module
2. Go to Settings > Technical > Company Location to configure countries and general managements
3. Configure branches from the same menu
4. Use the new fields in Company settings

Author: Augment Agent
License: LGPL-3
    """,
    'author': 'Augment Agent',
    'website': 'https://www.augmentcode.com',
    'license': 'LGPL-3',
    'depends': ['base', 'web'],
    'data': [
        'security/ir.model.access.csv',
        'data/demo_data.xml',
        'views/company_country_views.xml',
        'views/general_management_views.xml',
        'views/company_branch_views.xml',
        'views/res_company_views.xml',
        'views/menu_views.xml',
    ],
    'demo': [
        'data/demo_data.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'sequence': 100,
}
