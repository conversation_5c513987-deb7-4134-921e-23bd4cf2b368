#!/bin/bash

# Quick Installation Script for Company Location Fields Module
# Author: Augment Agent

echo "🚀 Company Location Fields - Quick Installation"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "__manifest__.py" ]; then
    print_error "Please run this script from the company_location_fields directory"
    exit 1
fi

print_info "Testing module structure..."

# Run Python test script if available
if [ -f "install_test.py" ]; then
    python3 install_test.py
    if [ $? -ne 0 ]; then
        print_error "Module tests failed. Please fix the issues first."
        exit 1
    fi
else
    print_warning "Test script not found, skipping tests"
fi

print_status "Module structure validated successfully!"

echo ""
print_info "Installation Instructions:"
echo "=========================="

echo "1. Copy this module to your Odoo addons directory:"
echo "   cp -r $(pwd) /path/to/odoo/addons/"
echo ""

echo "2. Restart your Odoo server:"
echo "   sudo systemctl restart odoo"
echo "   # OR"
echo "   sudo service odoo restart"
echo ""

echo "3. Update Apps List in Odoo:"
echo "   - Go to Apps menu"
echo "   - Click 'Update Apps List'"
echo "   - Wait for the update to complete"
echo ""

echo "4. Install the module:"
echo "   - Search for 'Company Location Fields'"
echo "   - Click 'Install'"
echo ""

echo "5. Configure the module:"
echo "   - Go to Settings → Technical → Company Location"
echo "   - Add your countries, general managements, and branches"
echo "   - Go to Settings → Companies → Manage Companies"
echo "   - Set the location fields for your companies"
echo ""

print_status "Module is ready for installation!"

echo ""
print_info "Module Features:"
echo "================"
echo "• Company Country field with custom countries"
echo "• General Management field linked to countries"
echo "• Company Branch field with different branch types"
echo "• Bilingual support (Arabic/English)"
echo "• Professional UI integration"
echo "• Demo data included"
echo "• Technical Settings configuration"

echo ""
print_info "Support:"
echo "========"
echo "• Check README.md for detailed documentation"
echo "• All fields are configurable from Technical Settings"
echo "• Demo data includes Saudi Arabia, UAE, Egypt, and Jordan"

echo ""
print_status "Installation script completed successfully!"
