# دليل التثبيت - Company Location Fields Module

## ✅ تم إنشاء الوحدة بنجاح!

تم إنشاء وحدة **Company Location Fields** بنجاح وجميع الاختبارات نجحت! الوحدة جاهزة للتثبيت والاستخدام.

## 📋 ملخص الوحدة

### الحقول المضافة:
1. **🌍 بلد الشركة (Company Country)** - قابل للإدارة من Technical Settings
2. **🏢 الإدارة العامة (General Management)** - مرتبط بالبلد المحدد
3. **🏪 فرع الشركة (Company Branch)** - قائمة منسدلة مخصصة

### الميزات:
- ✅ دعم ثنائي اللغة (العربية/الإنجليزية)
- ✅ علاقات هرمية (بلد ← إدارة عامة ← شركة)
- ✅ واجهة احترافية متوافقة مع Odoo
- ✅ بيانات تجريبية شاملة
- ✅ إعدادات تقنية سهلة الاستخدام

## 🚀 خطوات التثبيت

### 1. نسخ الوحدة
```bash
# انسخ مجلد الوحدة إلى مجلد addons في Odoo
cp -r company_location_fields /path/to/odoo/addons/
```

### 2. إعادة تشغيل Odoo
```bash
# إعادة تشغيل خدمة Odoo
sudo systemctl restart odoo
# أو
sudo service odoo restart
```

### 3. تحديث قائمة التطبيقات
1. اذهب إلى **Apps** في Odoo
2. انقر على **Update Apps List**
3. انتظر حتى اكتمال التحديث

### 4. تثبيت الوحدة
1. ابحث عن **"Company Location Fields"**
2. انقر على **Install**
3. انتظر حتى اكتمال التثبيت

## ⚙️ الإعداد والاستخدام

### إعداد البيانات الأساسية:

#### 1. إعداد البلدان
```
Settings → Technical → Company Location → Countries
```
- أضف البلدان المطلوبة
- حدد الرموز (مثل: SA, AE, EG)
- أضف الأوصاف

#### 2. إعداد الإدارات العامة
```
Settings → Technical → Company Location → General Managements
```
- أضف الإدارات العامة لكل بلد
- حدد المدراء ومعلومات الاتصال
- أضف العناوين

#### 3. إعداد الفروع
```
Settings → Technical → Company Location → Branches
```
- أضف أنواع الفروع (رئيسي، فرعي، مكتب، مستودع)
- حدد المدراء والعناوين

### استخدام الحقول:

#### في إعدادات الشركة:
```
Settings → Companies → Manage Companies
```
1. اختر **البلد**
2. اختر **الإدارة العامة** (ستظهر حسب البلد المحدد)
3. اختر **الفرع**

#### عرض التقارير:
```
Settings → Technical → Company Location → Companies by Location
```
- عرض الشركات مجمعة حسب الموقع
- فلترة وبحث متقدم

## 📊 البيانات التجريبية المتضمنة

### البلدان:
- 🇸🇦 المملكة العربية السعودية
- 🇦🇪 دولة الإمارات العربية المتحدة
- 🇪🇬 جمهورية مصر العربية
- 🇯🇴 المملكة الأردنية الهاشمية

### الإدارات العامة:
- **السعودية**: الرياض، جدة، المنطقة الشرقية
- **الإمارات**: دبي، أبوظبي
- **مصر**: القاهرة، الإسكندرية

### الفروع:
- المكتب الرئيسي
- الفرع الشمالي
- الفرع الجنوبي
- المستودع المركزي
- مكتب المبيعات

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة:

#### الحقول لا تظهر:
- تأكد من تثبيت الوحدة بشكل صحيح
- أعد تشغيل Odoo
- امسح الكاش

#### الإدارات العامة لا تظهر:
- تأكد من اختيار البلد أولاً
- تحقق من وجود إدارات عامة للبلد المحدد

#### مشاكل الترجمة:
- تأكد من تحديث ملفات الترجمة
- أعد تشغيل Odoo بعد تحديث الترجمات

## 📁 هيكل الملفات

```
company_location_fields/
├── __manifest__.py          # ملف التعريف
├── README.md               # دليل المستخدم
├── models/                 # النماذج
│   ├── company_country.py
│   ├── general_management.py
│   ├── company_branch.py
│   └── res_company.py
├── views/                  # الواجهات
│   ├── company_country_views.xml
│   ├── general_management_views.xml
│   ├── company_branch_views.xml
│   ├── res_company_views.xml
│   └── menu_views.xml
├── security/               # الصلاحيات
│   └── ir.model.access.csv
├── data/                   # البيانات
│   └── demo_data.xml
└── i18n/                   # الترجمات
    └── ar.po
```

## 🎯 النتيجة النهائية

بعد التثبيت، ستجد في نموذج الشركة ثلاثة حقول جديدة تحت حقل **Currency**:

1. **Company Country** - قائمة منسدلة بالبلدان
2. **General Management** - قائمة منسدلة بالإدارات العامة (مرتبطة بالبلد)
3. **Company Branch** - قائمة منسدلة بالفروع

## 📞 الدعم

- راجع ملف `README.md` للمزيد من التفاصيل
- جميع الحقول قابلة للتخصيص من Technical Settings
- البيانات التجريبية متضمنة للاختبار

---

**تم إنشاء الوحدة بواسطة**: Augment Agent  
**الترخيص**: LGPL-3  
**التوافق**: Odoo 15.0+
