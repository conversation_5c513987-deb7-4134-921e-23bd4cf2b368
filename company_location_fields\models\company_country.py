# -*- coding: utf-8 -*-

from odoo import models, fields, api


class CompanyCountry(models.Model):
    _name = 'company.country'
    _description = 'Company Country'
    _order = 'sequence, name'
    _rec_name = 'name'

    name = fields.Char(
        string='Country Name',
        required=True,
        translate=True,
        help='Name of the country'
    )
    code = fields.Char(
        string='Country Code',
        size=3,
        help='Country code (e.g., SA, AE, EG)'
    )
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Used to order countries'
    )
    active = fields.Boolean(
        string='Active',
        default=True,
        help='If unchecked, it will allow you to hide the country without removing it.'
    )
    description = fields.Text(
        string='Description',
        translate=True,
        help='Additional information about the country'
    )
    general_management_ids = fields.One2many(
        'general.management',
        'country_id',
        string='General Managements',
        help='General managements in this country'
    )
    company_ids = fields.One2many(
        'res.company',
        'company_country_id',
        string='Companies',
        help='Companies in this country'
    )

    @api.depends('name', 'code')
    def name_get(self):
        result = []
        for record in self:
            if record.code:
                name = f"[{record.code}] {record.name}"
            else:
                name = record.name
            result.append((record.id, name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        if name:
            # Search by name or code
            domain = ['|', ('name', operator, name), ('code', operator, name)]
            records = self.search(domain + args, limit=limit)
            return records.name_get()
        return super(CompanyCountry, self).name_search(name, args, operator, limit)

    @api.depends('general_management_ids')
    def _compute_general_managements_count(self):
        for record in self:
            record.general_managements_count = len(record.general_management_ids)

    general_managements_count = fields.Integer(
        string='General Managements Count',
        compute='_compute_general_managements_count',
        store=True,
        help='Number of general managements in this country'
    )
