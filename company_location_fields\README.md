# Company Location Fields

## نظرة عامة | Overview

هذه الوحدة تضيف ثلاثة حقول جديدة لنموذج الشركة في Odoo لتنظيم الشركات حسب الموقع الجغرافي والإداري.

This module adds three new fields to the Company model in Odoo to organize companies by geographical and administrative location.

## الميزات | Features

### الحقول الجديدة | New Fields

1. **بلد الشركة | Company Country**
   - قائمة منسدلة قابلة للتخصيص
   - إدارة من الإعدادات التقنية
   - دعم الرموز والأوصاف

2. **الإدارة العامة | General Management**
   - مرتبطة بالبلد المحدد
   - علاقة هرمية مع البلدان
   - معلومات المدير والاتصال

3. **فرع الشركة | Company Branch**
   - أنواع مختلفة من الفروع (رئيسي، فرعي، مكتب، مستودع)
   - معلومات المدير والعنوان
   - قابلة للتخصيص بالكامل

### الميزات التقنية | Technical Features

- **دعم ثنائي اللغة | Bilingual Support**: العربية والإنجليزية
- **واجهة احترافية | Professional UI**: تكامل مع تصميم Odoo الافتراضي
- **علاقات هرمية | Hierarchical Relations**: بلد ← إدارة عامة ← شركة
- **إعدادات تقنية | Technical Settings**: إدارة سهلة للبيانات
- **تقارير | Reports**: عرض الشركات حسب الموقع

## التثبيت | Installation

### المتطلبات | Requirements

- Odoo 15.0+
- وحدة `base`
- وحدة `web`

### خطوات التثبيت | Installation Steps

1. **نسخ الوحدة | Copy Module**
   ```bash
   cp -r company_location_fields /path/to/odoo/addons/
   ```

2. **إعادة تشغيل Odoo | Restart Odoo**
   ```bash
   sudo systemctl restart odoo
   ```

3. **تحديث قائمة التطبيقات | Update Apps List**
   - اذهب إلى Apps
   - انقر على "Update Apps List"

4. **تثبيت الوحدة | Install Module**
   - ابحث عن "Company Location Fields"
   - انقر على "Install"

## الاستخدام | Usage

### إعداد البيانات الأساسية | Basic Data Setup

1. **إعداد البلدان | Setup Countries**
   ```
   Settings → Technical → Company Location → Countries
   ```
   - أضف البلدان المطلوبة
   - حدد الرموز والأوصاف

2. **إعداد الإدارات العامة | Setup General Managements**
   ```
   Settings → Technical → Company Location → General Managements
   ```
   - أضف الإدارات العامة لكل بلد
   - حدد المدراء ومعلومات الاتصال

3. **إعداد الفروع | Setup Branches**
   ```
   Settings → Technical → Company Location → Branches
   ```
   - أضف أنواع الفروع المختلفة
   - حدد المدراء والعناوين

### استخدام الحقول | Using Fields

1. **في إعدادات الشركة | In Company Settings**
   ```
   Settings → Companies → Manage Companies
   ```
   - اختر البلد
   - اختر الإدارة العامة (ستظهر حسب البلد المحدد)
   - اختر الفرع

2. **في التقارير | In Reports**
   ```
   Settings → Technical → Company Location → Companies by Location
   ```
   - عرض الشركات مجمعة حسب الموقع
   - فلترة وبحث متقدم

## هيكل الملفات | File Structure

```
company_location_fields/
├── __init__.py
├── __manifest__.py
├── README.md
├── models/
│   ├── __init__.py
│   ├── company_country.py
│   ├── general_management.py
│   ├── company_branch.py
│   └── res_company.py
├── views/
│   ├── company_country_views.xml
│   ├── general_management_views.xml
│   ├── company_branch_views.xml
│   ├── res_company_views.xml
│   └── menu_views.xml
├── security/
│   └── ir.model.access.csv
├── data/
│   └── demo_data.xml
└── i18n/
    └── ar.po
```

## النماذج | Models

### company.country
- **الغرض | Purpose**: إدارة البلدان
- **الحقول الرئيسية | Main Fields**: name, code, sequence, description
- **العلاقات | Relations**: One2many مع general.management

### general.management
- **الغرض | Purpose**: إدارة الإدارات العامة
- **الحقول الرئيسية | Main Fields**: name, code, country_id, manager_name
- **العلاقات | Relations**: Many2one مع company.country

### company.branch
- **الغرض | Purpose**: إدارة فروع الشركات
- **الحقول الرئيسية | Main Fields**: name, code, branch_type, manager_name
- **الأنواع | Types**: main, sub, office, warehouse, other

## الصلاحيات | Permissions

- **مدراء النظام | System Administrators**: قراءة، كتابة، إنشاء، حذف
- **المستخدمون العاديون | Regular Users**: قراءة فقط

## البيانات التجريبية | Demo Data

تتضمن الوحدة بيانات تجريبية شاملة:

- **4 بلدان**: السعودية، الإمارات، مصر، الأردن
- **8 إدارات عامة**: موزعة على البلدان
- **5 فروع**: أنواع مختلفة من الفروع

## الدعم والصيانة | Support & Maintenance

### المشاكل الشائعة | Common Issues

1. **الحقول لا تظهر | Fields Not Showing**
   - تأكد من تثبيت الوحدة بشكل صحيح
   - أعد تشغيل Odoo

2. **الإدارات العامة لا تظهر | General Managements Not Showing**
   - تأكد من اختيار البلد أولاً
   - تحقق من وجود إدارات عامة للبلد المحدد

3. **مشاكل الترجمة | Translation Issues**
   - تأكد من تحديث ملفات الترجمة
   - أعد تشغيل Odoo بعد تحديث الترجمات

### التطوير المستقبلي | Future Development

- إضافة تقارير متقدمة
- دعم المزيد من اللغات
- تكامل مع وحدات أخرى
- إضافة خرائط جغرافية

## الترخيص | License

LGPL-3

## المؤلف | Author

Augment Agent - Augment Code

---

**ملاحظة**: هذه الوحدة تم تطويرها باستخدام أفضل الممارسات في تطوير Odoo وتتبع المعايير الرسمية للتصميم والبرمجة.

**Note**: This module was developed using Odoo development best practices and follows official design and programming standards.
