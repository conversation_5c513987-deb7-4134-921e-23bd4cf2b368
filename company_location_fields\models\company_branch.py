# -*- coding: utf-8 -*-

from odoo import models, fields, api


class CompanyBranch(models.Model):
    _name = 'company.branch'
    _description = 'Company Branch'
    _order = 'sequence, name'
    _rec_name = 'name'

    name = fields.Char(
        string='Branch Name',
        required=True,
        translate=True,
        help='Name of the branch'
    )
    code = fields.Char(
        string='Branch Code',
        size=10,
        help='Short code for the branch'
    )
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Used to order branches'
    )
    active = fields.Boolean(
        string='Active',
        default=True,
        help='If unchecked, it will allow you to hide the branch without removing it.'
    )
    description = fields.Text(
        string='Description',
        translate=True,
        help='Additional information about the branch'
    )
    address = fields.Text(
        string='Address',
        translate=True,
        help='Address of the branch'
    )
    phone = fields.Char(
        string='Phone',
        help='Phone number of the branch'
    )
    email = fields.Char(
        string='Email',
        help='Email address of the branch'
    )
    manager_name = fields.Char(
        string='Branch Manager',
        translate=True,
        help='Name of the branch manager'
    )
    branch_type = fields.Selection([
        ('main', 'Main Branch'),
        ('sub', 'Sub Branch'),
        ('office', 'Office'),
        ('warehouse', 'Warehouse'),
        ('other', 'Other')
    ], string='Branch Type', default='sub', help='Type of the branch')
    
    company_ids = fields.One2many(
        'res.company',
        'company_branch_id',
        string='Companies',
        help='Companies using this branch'
    )

    @api.depends('name', 'code', 'branch_type')
    def name_get(self):
        result = []
        for record in self:
            name_parts = []
            if record.code:
                name_parts.append(f"[{record.code}]")
            name_parts.append(record.name)
            if record.branch_type:
                type_dict = dict(record._fields['branch_type'].selection)
                name_parts.append(f"({type_dict.get(record.branch_type, '')})")
            name = " ".join(name_parts)
            result.append((record.id, name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        if name:
            # Search by name or code
            domain = ['|', ('name', operator, name), ('code', operator, name)]
            records = self.search(domain + args, limit=limit)
            return records.name_get()
        return super(CompanyBranch, self).name_search(name, args, operator, limit)

    @api.depends('company_ids')
    def _compute_companies_count(self):
        for record in self:
            record.companies_count = len(record.company_ids)

    companies_count = fields.Integer(
        string='Companies Count',
        compute='_compute_companies_count',
        store=True,
        help='Number of companies using this branch'
    )
