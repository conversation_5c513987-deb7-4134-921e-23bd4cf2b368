# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ResCompany(models.Model):
    _inherit = 'res.company'

    # Company Country Field
    company_country_id = fields.Many2one(
        'company.country',
        string='Company Country',
        help='Select the country for this company',
        domain=[('active', '=', True)]
    )

    # General Management Field (depends on country)
    general_management_id = fields.Many2one(
        'general.management',
        string='General Management',
        help='Select the general management for this company',
        domain="[('country_id', '=', company_country_id), ('active', '=', True)]"
    )

    # Company Branch Field
    company_branch_id = fields.Many2one(
        'company.branch',
        string='Company Branch',
        help='Select the branch for this company',
        domain=[('active', '=', True)]
    )

    @api.onchange('company_country_id')
    def _onchange_company_country_id(self):
        """Clear general management when country changes"""
        if self.company_country_id:
            # Clear general management if it doesn't belong to the selected country
            if self.general_management_id and self.general_management_id.country_id != self.company_country_id:
                self.general_management_id = False
            
            # Update domain for general management
            return {
                'domain': {
                    'general_management_id': [
                        ('country_id', '=', self.company_country_id.id),
                        ('active', '=', True)
                    ]
                }
            }
        else:
            # Clear general management if no country is selected
            self.general_management_id = False
            return {
                'domain': {
                    'general_management_id': [('id', '=', False)]
                }
            }

    @api.model
    def create(self, vals):
        """Override create to ensure data consistency"""
        # Validate general management belongs to selected country
        if vals.get('general_management_id') and vals.get('company_country_id'):
            general_management = self.env['general.management'].browse(vals['general_management_id'])
            if general_management.country_id.id != vals['company_country_id']:
                vals['general_management_id'] = False
        
        return super(ResCompany, self).create(vals)

    def write(self, vals):
        """Override write to ensure data consistency"""
        # Validate general management belongs to selected country
        for record in self:
            country_id = vals.get('company_country_id', record.company_country_id.id)
            general_management_id = vals.get('general_management_id', record.general_management_id.id)
            
            if general_management_id and country_id:
                general_management = self.env['general.management'].browse(general_management_id)
                if general_management.country_id.id != country_id:
                    vals['general_management_id'] = False
        
        return super(ResCompany, self).write(vals)

    def get_location_info(self):
        """Helper method to get formatted location information"""
        location_parts = []
        
        if self.company_country_id:
            location_parts.append(self.company_country_id.name)
        
        if self.general_management_id:
            location_parts.append(self.general_management_id.name)
        
        if self.company_branch_id:
            location_parts.append(self.company_branch_id.name)
        
        return " - ".join(location_parts) if location_parts else ""

    def action_view_country_general_managements(self):
        """Action to view general managements of the company's country"""
        if not self.company_country_id:
            return False
        
        return {
            'type': 'ir.actions.act_window',
            'name': f'General Managements in {self.company_country_id.name}',
            'res_model': 'general.management',
            'view_mode': 'tree,form',
            'domain': [('country_id', '=', self.company_country_id.id)],
            'context': {'default_country_id': self.company_country_id.id},
            'target': 'current',
        }
