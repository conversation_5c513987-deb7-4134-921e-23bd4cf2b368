<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Inherit Company Form View -->
        <record id="view_company_form_inherit_location" model="ir.ui.view">
            <field name="name">res.company.form.inherit.location</field>
            <field name="model">res.company</field>
            <field name="inherit_id" ref="base.view_company_form"/>
            <field name="arch" type="xml">
                <!-- Add fields after Currency field -->
                <xpath expr="//field[@name='currency_id']" position="after">
                    <field name="company_country_id" 
                           options="{'no_create': True, 'no_edit': True}"
                           placeholder="Select Country..."/>
                    <field name="general_management_id" 
                           options="{'no_create': True, 'no_edit': True}"
                           placeholder="Select General Management..."
                           attrs="{'invisible': [('company_country_id', '=', False)]}"/>
                    <field name="company_branch_id" 
                           options="{'no_create': True, 'no_edit': True}"
                           placeholder="Select Branch..."/>
                </xpath>
            </field>
        </record>

        <!-- Company Tree View Enhancement -->
        <record id="view_company_tree_inherit_location" model="ir.ui.view">
            <field name="name">res.company.tree.inherit.location</field>
            <field name="model">res.company</field>
            <field name="inherit_id" ref="base.view_company_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="company_country_id" optional="show"/>
                    <field name="general_management_id" optional="hide"/>
                    <field name="company_branch_id" optional="hide"/>
                </xpath>
            </field>
        </record>

        <!-- Company Search View Enhancement -->
        <record id="view_company_search_inherit_location" model="ir.ui.view">
            <field name="name">res.company.search.inherit.location</field>
            <field name="model">res.company</field>
            <field name="inherit_id" ref="base.view_company_search"/>
            <field name="arch" type="xml">
                <xpath expr="//search" position="inside">
                    <field name="company_country_id" string="Country"/>
                    <field name="general_management_id" string="General Management"/>
                    <field name="company_branch_id" string="Branch"/>
                    
                    <separator/>
                    <filter string="By Country" name="group_by_country" 
                            context="{'group_by': 'company_country_id'}"/>
                    <filter string="By General Management" name="group_by_general_management" 
                            context="{'group_by': 'general_management_id'}"/>
                    <filter string="By Branch" name="group_by_branch" 
                            context="{'group_by': 'company_branch_id'}"/>
                </xpath>
            </field>
        </record>

        <!-- Company Kanban View Enhancement -->
        <record id="view_company_kanban_inherit_location" model="ir.ui.view">
            <field name="name">res.company.kanban.inherit.location</field>
            <field name="model">res.company</field>
            <field name="inherit_id" ref="base.view_company_kanban"/>
            <field name="arch" type="xml">
                <xpath expr="//div[hasclass('oe_kanban_details')]" position="inside">
                    <div t-if="record.company_country_id.raw_value" class="o_kanban_record_subtitle">
                        <i class="fa fa-globe" title="Country"/> 
                        <field name="company_country_id"/>
                    </div>
                    <div t-if="record.general_management_id.raw_value" class="o_kanban_record_subtitle">
                        <i class="fa fa-building" title="General Management"/> 
                        <field name="general_management_id"/>
                    </div>
                    <div t-if="record.company_branch_id.raw_value" class="o_kanban_record_subtitle">
                        <i class="fa fa-map-marker" title="Branch"/> 
                        <field name="company_branch_id"/>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
